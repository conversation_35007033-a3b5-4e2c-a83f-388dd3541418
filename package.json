{"name": "murai-mobile", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-google-signin/google-signin": "^15.0.0", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.3", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@reduxjs/toolkit": "^2.8.2", "@tamagui/animations-react-native": "^1.130.8", "@tamagui/button": "^1.132.6", "@tamagui/config": "^1.132.6", "@tamagui/core": "^1.132.6", "@tamagui/font-inter": "^1.130.8", "@tamagui/input": "^1.130.8", "@tamagui/react-native-media-driver": "^1.130.8", "@tamagui/shorthands": "^1.130.8", "@tamagui/stacks": "^1.132.6", "@tamagui/text": "^1.132.6", "@tamagui/theme-base": "^1.130.8", "@tamagui/themes": "^1.130.8", "axios": "^1.10.0", "crypto-browserify": "^3.12.1", "expo": "~53.0.17", "expo-auth-session": "^6.2.1", "expo-blur": "~14.1.5", "expo-constants": "~17.1.7", "expo-crypto": "^14.1.5", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lodash": "^4.17.21", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "~4.11.1", "react-native-svg": "^12.5.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "stream-browserify": "^3.0.0", "tailwindcss": "^3.4.17", "tamagui": "^1.132.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}